<template>
  <div ref="vantaRef" class="login">
    <div class="left">
      <div class="p1">智慧则南邻里办管理平台</div>
      <div class="p2">欢迎登录</div>
    </div>
    <div class="right">
      <div class="tit">账号登陆</div>
      <input
        v-model="loginForm.username"
        type="text"
        placeholder="请输入账号"
        class="ipt1"
        @blur="input1"
      />
      <img src="../assets/images/user.png" alt="" class="user" />
      <div v-show="is1" class="not1">请输入账号！</div>
      <input
        v-model="loginForm.password"
        type="password"
        placeholder="请输入密码"
        class="ipt2"
        data="passworedtype"
        @blur="input2"
      />
      <img src="../assets/images/password.png" alt="" class="password" />
      <div v-show="is2" class="not2">请输入密码！</div>
      <div class="btn" @click="handleLogin">登录</div>
    </div>
    <!--  底部  -->
    <!--    <div class="el-login-footer">-->
    <!--      <span>-->
    <!--        <a href="https://sourcebyte.vip" target="_blank"-->
    <!--        >Copyright © 2022-2023 邻里办小程序后台 Open Source Byte All Rights-->
    <!--          Reserved.</a-->
    <!--        >-->
    <!--      </span>-->
    <!--    </div>-->
  </div>
</template>

<script>
import * as THREE from "three";
import Clouds from "vanta/src/vanta.clouds";

import axios from "axios";
import request from "@/utils/request";
import { Message, Notification } from "element-ui";
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { decrypt, encrypt } from "@/utils/jsencrypt";
import "element-ui/lib/theme-chalk/display.css";

export default {
  name: "Login",
  data() {
    return {
      is1: false,
      is2: false,
      loginType: 0,
      ws: null,
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      loading: false,
      // 验证码开关
      captchaOnOff: false,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  async created() {
    this.getCode();
    this.getCookie();
    if (!this.ws) {
      this.ws = new WebSocket(this.wsuri);
    }
    await new Promise((resolve) => {
      this.ws.onopen = () => {
        console.log("已经打开连接!");
        resolve(); // 等待连接成功 再发送消息并执行后续代码
      };
    });
    const _this = this;
    this.ws.onmessage = function (event) {
      // 获取后台返回的信息
      const subscribe = event.data;
      if (subscribe == 1) {
        // 关注了，则跳转到首页
        // Notification.warning("请使用密码登录");
        _this.qrCodeLogin();
      } else if (subscribe == 0) {
        Notification.success("分享是一种美德，请点赞关注支持");
      }
    };
    // 获取wx码
    this.getWxCode();
  },
  mounted() {
    this.vantaEffect = Clouds({
      el: this.$refs.vantaRef,
      THREE: THREE,
      skyColor: 0x5ca6ca,
      cloudShadowColor: 0x5c6c7a,
      speed: 2,
    });
  },
  beforeDestroy() {
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
  },
  methods: {
    input1() {
      debugger;
      if (this.loginForm.username) {
        this.is1 = false;
      } else {
        this.is1 = true;
      }
    },
    input2() {
      debugger;
      if (this.loginForm.password) {
        this.is2 = false;
      } else {
        this.is2 = true;
      }
    },
    getCode() {
      getCodeImg().then((res) => {
        this.captchaOnOff =
          res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      if (this.is1 || this.is2) return;
      // this.$refs.loginForm.validate((valid) => {
      //   if (valid) {
      this.loading = true;
      if (this.loginForm.rememberMe) {
        Cookies.set("username", this.loginForm.username, { expires: 30 });
        Cookies.set("password", encrypt(this.loginForm.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", this.loginForm.rememberMe, {
          expires: 30,
        });
      } else {
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      this.$store
        .dispatch("Login", this.loginForm)
        .then(() => {
          this.$router.push({ path: this.redirect || "/" }).catch(() => {});
        })
        .catch(() => {
          this.loading = false;
          if (this.captchaOnOff) {
            this.getCode();
          }
        });
      // }
      // });
    },
    changeLoginType(type) {
      this.loginType = type;
    },
    getWxCode() {
      // 先获取token
      request({
        url: "/api/cmsWxApi/getAccessToken",
        headers: {
          isToken: false,
        },
      }).then((res1) => {
        // 2000次调用上限
        const access_token = res1;
        const data = {
          expire_seconds: 604800,
          action_name: "QR_SCENE",
          action_info: { scene: { scene_id: 123 } },
        };
        // 再获取票据
        axios
          .post(
            "/wx-api/cgi-bin/qrcode/create?access_token=" + access_token,
            data
          )
          .then((res2) => {
            const ticket = res2.data.ticket;
            // 设置连接（session）与ticket对应关系
            if (this.ws) {
              this.ws.send(ticket);
            } else {
              Message({
                message: "未连接到服务器",
                type: "error",
              });
            }
            axios({
              method: "get",
              url: "/mp-api/cgi-bin/showqrcode?ticket=" + ticket,
              responseType: "arraybuffer",
            })
              .then((res3) => {
                // base64图片处理
                return (
                  "data:image/png;base64," +
                  btoa(
                    new Uint8Array(res3.data).reduce(
                      (data, byte) => data + String.fromCharCode(byte),
                      ""
                    )
                  )
                );
              })
              .then((data) => {
                this.wxQrcode = data;
              });
          });
      });
    },
    qrCodeLogin() {
      this.$store
        .dispatch("qrCodeLogin", {})
        .then(() => {
          this.$router.push({ path: this.redirect || "/" }).catch(() => {});
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.login {
  width: 100%;
  height: 100%;
  // background: url(/bg.png) no-repeat;
  position: relative;

  .left {
    width: 956px;
    height: 240px;
    background: url(../assets/images/ju2.png) no-repeat;
    background-size: 100%;
    position: absolute;
    top: 430px;
    left: 99px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .p1 {
      font-size: 44px;
      // font-family: ziti;
      font-weight: 400;
      color: #ffffff;
      margin-bottom: 20px;
    }

    .p2 {
      font-size: 24px;
      // font-family: Source Han Sans CN;
      color: #ffffff;
      opacity: 0.8;
    }
  }

  .right {
    width: 499px;
    height: 489px;
    background: url(../assets/images/ju.png) no-repeat;
    background-size: 100%;
    position: absolute;
    top: 306px;
    right: 150px;

    .tit {
      font-size: 24px;
      font-weight: 500;
      color: #ffffff;
      position: relative;
      top: 80px;
      left: 50px;
    }

    .ipt1 {
      width: 360px;
      height: 60px;
      background: transparent;
      border: 2px solid white;
      position: relative;
      top: 100px;
      left: 70px;
      padding-left: 40px;
      font-size: 18px;
      color: white;
    }

    .ipt2 {
      width: 360px;
      height: 60px;
      background: transparent;
      border: 2px solid white;
      position: relative;
      top: 130px;
      left: 70px;
      padding-left: 40px;
      font-size: 18px;
      color: white;
    }

    .user {
      position: relative;
      top: 100px;
      right: 274px;
    }

    .password {
      position: relative;
      top: 130px;
      right: 274px;
    }

    .btn {
      text-align: center;
      font-size: 18px;
      color: white;
      width: 360px;
      height: 60px;
      background: #0070b7;
      border-radius: 4px;
      line-height: 60px;
      position: relative;
      top: 200px;
      left: 70px;
      cursor: pointer;
    }

    .not1 {
      color: red;
      position: absolute;
      top: 200px;
      left: 50px;
      height: 14px;
      font-weight: bold;
      font-size: 14px;
    }

    .not2 {
      color: red;
      position: absolute;
      top: 300px;
      left: 50px;
      height: 14px;
      font-weight: bold;
      font-size: 14px;
    }
  }
}

::-webkit-input-placeholder {
  /* WebKit browsers，webkit内核浏览器 */
  color: #ccc;
  font-size: 16px;
}
</style>
