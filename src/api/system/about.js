import request from '@/utils/request'

// 查询公告列表
export function listAbout (query) {
  return request({
    url: '/system/about/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getAbout (noticeId) {
  return request({
    url: '/system/about/' + noticeId,
    method: 'get'
  })
}


// 修改公告
export function updateAbout (data) {
  return request({
    url: '/system/about',
    method: 'put',
    data: data
  })
}
