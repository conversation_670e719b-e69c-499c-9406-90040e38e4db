import request from '@/utils/request'

// 查询信息列表
export function listMessage (query) {
  return request({
    url: '/userMessage/message/list',
    method: 'get',
    params: query
  })
}

// 查询信息详细
export function getMessage (messageId) {
  return request({
    url: '/userMessage/message/' + messageId,
    method: 'get'
  })
}

// 新增信息
export function addMessage (data) {
  return request({
    url: '/userMessage/message',
    method: 'post',
    data: data
  })
}

// 修改信息
export function updateMessage (data) {
  return request({
    url: '/userMessage/message',
    method: 'put',
    data: data
  })
}

// 删除信息
export function delMessage (messageId) {
  return request({
    url: '/userMessage/message/' + messageId,
    method: 'delete'
  })
}
