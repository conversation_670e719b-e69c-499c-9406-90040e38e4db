{"name": "sourcebyte", "version": "2.0.0", "description": "邻里办小程序后台", "author": "詹Sir", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "gitee", "url": "https://gitee.com/open-source-byte/source-vue"}, "dependencies": {"@babel/parser": "^7.7.4", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "bpmn-js-token-simulation": "^0.10.0", "clipboard": "2.0.8", "core-js": "3.19.1", "echarts": "4.9.0", "element-china-area-data": "^5.0.2", "element-ui": "2.15.8", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "^10.5.0", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "three": "^0.121.0", "vanta": "^0.5.24", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xml-js": "^1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "bpmn-js": "^7.4.0", "bpmn-js-properties-panel": "^0.37.2", "camunda-bpmn-moddle": "^4.4.1", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}